# VESLINT Frontend Environment Configuration
# Copy this file to .env.local and fill in your actual values

# =============================================================================
# API CONFIGURATION
# =============================================================================

# Backend API URL
NEXT_PUBLIC_API_URL=http://localhost:8000

# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================

# Supabase Configuration (for real-time subscriptions and direct DB access)
# Get these from: https://app.supabase.com/project/YOUR_PROJECT/settings/api
NEXT_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...your-anon-key

# =============================================================================
# FIREBASE CONFIGURATION
# =============================================================================

# Firebase Web App Configuration (for authentication)
# Get these from: https://console.firebase.google.com/project/YOUR_PROJECT/settings/general
NEXT_PUBLIC_FIREBASE_API_KEY=your-web-api-key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
NEXT_PUBLIC_FIREBASE_APP_ID=your-app-id

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Application Settings
NEXT_PUBLIC_APP_NAME=VESLINT
NEXT_PUBLIC_APP_VERSION=2.0.0
NEXT_PUBLIC_ENVIRONMENT=development

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=false
NEXT_PUBLIC_ENABLE_DEBUG=true

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

# Development mode settings
NEXT_PUBLIC_DEV_MODE=true
NEXT_PUBLIC_MOCK_AUTH=false

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================

# Vercel deployment settings (automatically set by Vercel)
# VERCEL_URL=your-app.vercel.app
# VERCEL_ENV=production

# =============================================================================
# OPTIONAL INTEGRATIONS
# =============================================================================

# Analytics (if using)
# NEXT_PUBLIC_GA_TRACKING_ID=G-XXXXXXXXXX
# NEXT_PUBLIC_MIXPANEL_TOKEN=your-mixpanel-token

# Error tracking (if using)
# NEXT_PUBLIC_SENTRY_DSN=your-sentry-dsn

# =============================================================================
# NOTES
# =============================================================================

# 1. All frontend environment variables must be prefixed with NEXT_PUBLIC_
# 2. Never commit .env.local to version control
# 3. For production, set these in your deployment platform (Vercel, Netlify, etc.)
# 4. The Firebase config values are safe to expose in frontend code
# 5. Make sure your Supabase RLS policies are properly configured for security
