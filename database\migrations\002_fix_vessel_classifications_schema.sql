-- VESLINT Database Schema Fix
-- Migration 002: Fix vessel_classifications table to match Python models

-- First, drop dependent views and triggers
DROP VIEW IF EXISTS job_statistics CASCADE;
DROP VIEW IF EXISTS user_analytics CASCADE;
DROP TRIGGER IF EXISTS update_job_progress_trigger ON vessel_classifications;
DROP FUNCTION IF EXISTS update_job_progress();

-- Drop existing vessel_classifications table and recreate with correct schema
DROP TABLE IF EXISTS vessel_classifications CASCADE;

-- Recreate vessel_classifications table with correct schema
CREATE TABLE vessel_classifications (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    job_id UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
    
    -- Vessel identification (matching Python schema)
    mmsi BIGINT,
    imo BIGINT,
    vessel_name TEXT,
    call_sign TEXT,
    
    -- Classification results
    predicted_type TEXT CHECK (predicted_type IN ('TUG', 'FISHING', 'PLEASURE', 'CARGO')),
    confidence REAL CHECK (confidence >= 0 AND confidence <= 1),
    
    -- JSON fields for flexible data storage
    class_probabilities JSONB,
    features JSON<PERSON>,
    raw_data JSONB,
    
    -- Input data summary
    data_points INTEGER,
    time_span_hours REAL,
    first_seen TIMESTAMP WITH TIME ZONE,
    last_seen TIMESTAMP WITH TIME ZONE,
    
    -- Processing metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(job_id, mmsi)
);

-- Fix jobs table to match Python models
ALTER TABLE jobs DROP COLUMN IF EXISTS tug_count;
ALTER TABLE jobs DROP COLUMN IF EXISTS fishing_count;
ALTER TABLE jobs DROP COLUMN IF EXISTS pleasure_count;
ALTER TABLE jobs DROP COLUMN IF EXISTS cargo_count;

-- Add missing columns to jobs table if they don't exist
ALTER TABLE jobs ADD COLUMN IF NOT EXISTS title TEXT;
ALTER TABLE jobs ADD COLUMN IF NOT EXISTS description TEXT;
ALTER TABLE jobs ADD COLUMN IF NOT EXISTS job_type TEXT DEFAULT 'vessel_classification' CHECK (job_type IN ('vessel_classification', 'data_analysis'));

-- Create user_profiles table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id TEXT NOT NULL UNIQUE, -- Firebase Auth user ID
    email TEXT NOT NULL UNIQUE,
    display_name TEXT,
    role TEXT NOT NULL DEFAULT 'user' CHECK (role IN ('admin', 'user', 'analyst')),
    organization TEXT,
    preferences JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for user_profiles
CREATE INDEX idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX idx_user_profiles_email ON user_profiles(email);
CREATE INDEX idx_user_profiles_role ON user_profiles(role);

-- Create trigger for user_profiles updated_at
CREATE TRIGGER update_user_profiles_updated_at
    BEFORE UPDATE ON user_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for vessel classifications
CREATE INDEX idx_vessel_classifications_job_id ON vessel_classifications(job_id);
CREATE INDEX idx_vessel_classifications_mmsi ON vessel_classifications(mmsi);
CREATE INDEX idx_vessel_classifications_imo ON vessel_classifications(imo);
CREATE INDEX idx_vessel_classifications_predicted_type ON vessel_classifications(predicted_type);
CREATE INDEX idx_vessel_classifications_confidence ON vessel_classifications(confidence DESC);
CREATE INDEX idx_vessel_classifications_job_mmsi ON vessel_classifications(job_id, mmsi);
CREATE INDEX idx_vessel_classifications_job_type ON vessel_classifications(job_id, predicted_type);

-- Update job_statistics view to work with new schema
DROP VIEW IF EXISTS job_statistics CASCADE;
CREATE OR REPLACE VIEW job_statistics AS
SELECT 
    j.id,
    j.user_id,
    j.filename,
    j.status,
    j.created_at,
    j.updated_at,
    j.total_vessels,
    j.processed_vessels,
    j.progress_percentage,
    COALESCE(vc_stats.classified_vessels, 0) as classified_vessels,
    COALESCE(vc_stats.avg_confidence, 0) as avg_confidence,
    COALESCE(vc_stats.tug_count, 0) as tug_count,
    COALESCE(vc_stats.fishing_count, 0) as fishing_count,
    COALESCE(vc_stats.pleasure_count, 0) as pleasure_count,
    COALESCE(vc_stats.cargo_count, 0) as cargo_count
FROM jobs j
LEFT JOIN (
    SELECT 
        job_id,
        COUNT(*) as classified_vessels,
        AVG(confidence) as avg_confidence,
        COUNT(CASE WHEN predicted_type = 'TUG' THEN 1 END) as tug_count,
        COUNT(CASE WHEN predicted_type = 'FISHING' THEN 1 END) as fishing_count,
        COUNT(CASE WHEN predicted_type = 'PLEASURE' THEN 1 END) as pleasure_count,
        COUNT(CASE WHEN predicted_type = 'CARGO' THEN 1 END) as cargo_count
    FROM vessel_classifications 
    GROUP BY job_id
) vc_stats ON j.id = vc_stats.job_id;

-- Update user_analytics view
DROP VIEW IF EXISTS user_analytics CASCADE;
CREATE OR REPLACE VIEW user_analytics AS
SELECT 
    user_id,
    COUNT(DISTINCT j.id) as total_jobs,
    COUNT(DISTINCT CASE WHEN j.status = 'completed' THEN j.id END) as completed_jobs,
    COUNT(DISTINCT CASE WHEN j.status = 'failed' THEN j.id END) as failed_jobs,
    COALESCE(SUM(j.total_vessels), 0) as total_vessels_processed,
    COALESCE(AVG(vc_stats.avg_confidence), 0) as overall_avg_confidence,
    MAX(j.created_at) as last_job_date,
    COALESCE(SUM(vc_stats.classified_vessels), 0) as total_classifications
FROM jobs j
LEFT JOIN (
    SELECT 
        job_id,
        COUNT(*) as classified_vessels,
        AVG(confidence) as avg_confidence
    FROM vessel_classifications 
    GROUP BY job_id
) vc_stats ON j.id = vc_stats.job_id
GROUP BY user_id;

-- Create updated trigger function for job progress
CREATE OR REPLACE FUNCTION update_job_progress()
RETURNS TRIGGER AS $$
BEGIN
    -- Update processed_vessels count
    UPDATE jobs SET
        processed_vessels = (
            SELECT COUNT(*)
            FROM vessel_classifications
            WHERE job_id = COALESCE(NEW.job_id, OLD.job_id)
        ),
        progress_percentage = CASE
            WHEN total_vessels > 0 THEN
                ROUND((SELECT COUNT(*) FROM vessel_classifications WHERE job_id = COALESCE(NEW.job_id, OLD.job_id))::numeric / total_vessels * 100, 2)
            ELSE 0
        END
    WHERE id = COALESCE(NEW.job_id, OLD.job_id);

    RETURN COALESCE(NEW, OLD);
END;
$$ language 'plpgsql';

-- Create trigger for job progress updates
CREATE TRIGGER update_job_progress_trigger
    AFTER INSERT OR UPDATE OR DELETE ON vessel_classifications
    FOR EACH ROW EXECUTE FUNCTION update_job_progress();

-- Add comments for documentation
COMMENT ON TABLE vessel_classifications IS 'Stores individual vessel classification results with flexible JSON fields';
COMMENT ON COLUMN vessel_classifications.class_probabilities IS 'JSON object containing probability scores for all vessel classes';
COMMENT ON COLUMN vessel_classifications.features IS 'JSON object containing extracted features used for classification';
COMMENT ON COLUMN vessel_classifications.raw_data IS 'JSON object containing original AIS data points for the vessel';

-- Success message
SELECT 'Vessel classifications schema updated successfully!' as message;
