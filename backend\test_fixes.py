#!/usr/bin/env python3
"""
Test script to verify all the data management and backend fixes
Run this to check if the major issues have been resolved
"""

import asyncio
import os
import sys
import logging
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_imports():
    """Test that all modules can be imported without errors"""
    print("🔍 Testing imports...")
    
    try:
        # Test core imports
        from src.main import app
        print("✅ Main app imports successfully")
        
        from src.database.init import get_database_manager
        print("✅ Database manager imports successfully")
        
        from src.services.ml_service import VesselClassifier
        print("✅ ML service imports successfully")
        
        from src.utils.auth import verify_firebase_token
        print("✅ Auth utils import successfully")
        
        from src.models.vessel import VesselClassificationResponse
        print("✅ Vessel models import successfully")
        
        return True
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

async def test_environment_config():
    """Test environment configuration"""
    print("\n🔍 Testing environment configuration...")
    
    # Check if development mode works
    os.environ['DEVELOPMENT_MODE'] = 'true'
    os.environ['ALLOW_ANONYMOUS_ACCESS'] = 'true'
    
    try:
        from src.utils.auth import verify_firebase_token
        
        # Test development mode auth
        mock_credentials = None
        result = await verify_firebase_token(mock_credentials)
        
        if result and result.get('uid') == 'dev-user-123':
            print("✅ Development mode authentication works")
            return True
        else:
            print("❌ Development mode authentication failed")
            return False
            
    except Exception as e:
        print(f"❌ Environment config test failed: {e}")
        return False

async def test_ml_service():
    """Test ML service initialization"""
    print("\n🔍 Testing ML service...")
    
    try:
        from src.services.ml_service import VesselClassifier
        
        # Set development mode
        os.environ['DEVELOPMENT_MODE'] = 'true'
        os.environ['ENVIRONMENT'] = 'development'
        
        classifier = VesselClassifier()
        await classifier.initialize()
        
        if classifier.model_loaded:
            print("✅ ML service initializes successfully (with fallback model)")
            return True
        else:
            print("❌ ML service failed to initialize")
            return False
            
    except Exception as e:
        print(f"❌ ML service test failed: {e}")
        return False

async def test_database_schemas():
    """Test database schema definitions"""
    print("\n🔍 Testing database schemas...")
    
    try:
        from src.database.schemas import DatabaseSchemas, validate_schema
        
        schemas = DatabaseSchemas()
        
        # Test vessel classifications schema
        vessel_schema = schemas.vessel_classifications_schema
        if vessel_schema and 'columns' in vessel_schema:
            print("✅ Vessel classifications schema is defined")
            
            # Check for new schema fields
            columns = vessel_schema['columns']
            required_fields = ['mmsi', 'predicted_type', 'confidence', 'class_probabilities']
            
            missing_fields = [field for field in required_fields if field not in columns]
            if not missing_fields:
                print("✅ All required schema fields are present")
                return True
            else:
                print(f"❌ Missing schema fields: {missing_fields}")
                return False
        else:
            print("❌ Vessel classifications schema not found")
            return False
            
    except Exception as e:
        print(f"❌ Database schema test failed: {e}")
        return False

async def test_api_structure():
    """Test API route structure"""
    print("\n🔍 Testing API structure...")
    
    try:
        from src.api.routes import ROUTE_GROUPS
        
        expected_routes = ['health', 'jobs', 'vessels']
        found_routes = [group['prefix'].strip('/') for group in ROUTE_GROUPS]
        
        missing_routes = [route for route in expected_routes if route not in found_routes]
        
        if not missing_routes:
            print("✅ All expected API routes are configured")
            return True
        else:
            print(f"❌ Missing API routes: {missing_routes}")
            return False
            
    except Exception as e:
        print(f"❌ API structure test failed: {e}")
        return False

async def test_validation():
    """Test data validation functions"""
    print("\n🔍 Testing data validation...")
    
    try:
        from src.utils.validation import validate_csv_file
        
        # Test with minimal valid CSV
        test_csv = b"mmsi,timestamp,lat,lon,sog,cog,heading\n123456789,2024-01-01T00:00:00Z,40.7128,-74.0060,5.0,90.0,90.0\n"
        
        result = await validate_csv_file(test_csv)
        
        if result.get('valid'):
            print("✅ CSV validation works correctly")
            return True
        else:
            print(f"❌ CSV validation failed: {result.get('errors', [])}")
            return False
            
    except Exception as e:
        print(f"❌ Validation test failed: {e}")
        return False

async def run_all_tests():
    """Run all tests and report results"""
    print("🚀 Starting VESLINT Data Management Fix Verification\n")
    
    tests = [
        ("Imports", test_imports),
        ("Environment Config", test_environment_config),
        ("ML Service", test_ml_service),
        ("Database Schemas", test_database_schemas),
        ("API Structure", test_api_structure),
        ("Data Validation", test_validation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*50)
    print("📊 TEST RESULTS SUMMARY")
    print("="*50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The fixes are working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(level=logging.WARNING)  # Reduce noise
    
    # Run tests
    success = asyncio.run(run_all_tests())
    sys.exit(0 if success else 1)
