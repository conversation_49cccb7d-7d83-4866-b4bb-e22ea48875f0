"use client";

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Avatar,
  Chip,
  Divider,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
} from '@mui/material';
import { Person, Edit, Save, Cancel } from '@mui/icons-material';
import { useAuth } from '@/hooks/useAuth';
import { usersApi } from '@/lib/api';

interface UserProfile {
  id: string;
  user_id: string;
  email: string;
  display_name?: string;
  role: 'admin' | 'user' | 'analyst';
  organization?: string;
  preferences?: {
    theme: string;
    notifications_enabled: boolean;
    default_file_format: string;
    timezone: string;
    language: string;
  };
  created_at: string;
  updated_at?: string;
}

interface UserStats {
  user_id: string;
  total_jobs: number;
  completed_jobs: number;
  failed_jobs: number;
  total_vessels_processed: number;
  last_job_date?: string;
  member_since: string;
}

export default function UserProfile() {
  const { user } = useAuth();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [stats, setStats] = useState<UserStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [editing, setEditing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Form state
  const [formData, setFormData] = useState({
    display_name: '',
    organization: '',
    preferences: {
      theme: 'light',
      notifications_enabled: true,
      default_file_format: 'csv',
      timezone: 'UTC',
      language: 'en',
    }
  });

  useEffect(() => {
    if (user) {
      loadUserData();
    }
  }, [user]);

  const loadUserData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load profile and stats in parallel
      const [profileResponse, statsResponse] = await Promise.all([
        usersApi.getProfile(),
        usersApi.getStats()
      ]);

      if (profileResponse.success) {
        setProfile(profileResponse.data);
        setFormData({
          display_name: profileResponse.data.display_name || '',
          organization: profileResponse.data.organization || '',
          preferences: {
            theme: profileResponse.data.preferences?.theme || 'light',
            notifications_enabled: profileResponse.data.preferences?.notifications_enabled ?? true,
            default_file_format: profileResponse.data.preferences?.default_file_format || 'csv',
            timezone: profileResponse.data.preferences?.timezone || 'UTC',
            language: profileResponse.data.preferences?.language || 'en',
          }
        });
      }

      if (statsResponse.success) {
        setStats(statsResponse.data);
      }
    } catch (err) {
      console.error('Error loading user data:', err);
      setError('Failed to load user profile');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);
      setSuccess(null);

      const updateData = {
        display_name: formData.display_name || null,
        organization: formData.organization || null,
        preferences: formData.preferences
      };

      const response = await usersApi.updateProfile(updateData);

      if (response.success) {
        setProfile(response.data);
        setEditing(false);
        setSuccess('Profile updated successfully');
      } else {
        setError('Failed to update profile');
      }
    } catch (err) {
      console.error('Error updating profile:', err);
      setError('Failed to update profile');
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    if (profile) {
      setFormData({
        display_name: profile.display_name || '',
        organization: profile.organization || '',
        preferences: {
          theme: profile.preferences?.theme || 'light',
          notifications_enabled: profile.preferences?.notifications_enabled ?? true,
          default_file_format: profile.preferences?.default_file_format || 'csv',
          timezone: profile.preferences?.timezone || 'UTC',
          language: profile.preferences?.language || 'en',
        }
      });
    }
    setEditing(false);
    setError(null);
    setSuccess(null);
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (!profile) {
    return (
      <Alert severity="error">
        Failed to load user profile. Please try refreshing the page.
      </Alert>
    );
  }

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}
      
      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      )}

      {/* Profile Header */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
            <Box display="flex" alignItems="center" gap={2}>
              <Avatar sx={{ width: 64, height: 64 }}>
                <Person />
              </Avatar>
              <Box>
                <Typography variant="h5">
                  {profile.display_name || 'User Profile'}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {profile.email}
                </Typography>
                <Chip 
                  label={profile.role.toUpperCase()} 
                  size="small" 
                  color={profile.role === 'admin' ? 'primary' : 'default'}
                  sx={{ mt: 1 }}
                />
              </Box>
            </Box>
            
            <Button
              variant={editing ? "outlined" : "contained"}
              startIcon={editing ? <Cancel /> : <Edit />}
              onClick={editing ? handleCancel : () => setEditing(true)}
              disabled={saving}
            >
              {editing ? 'Cancel' : 'Edit Profile'}
            </Button>
          </Box>

          {/* User Statistics */}
          {stats && (
            <Grid container spacing={2} sx={{ mt: 2 }}>
              <Grid item xs={6} sm={3}>
                <Typography variant="h6" color="primary">
                  {stats.total_jobs}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Jobs
                </Typography>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Typography variant="h6" color="success.main">
                  {stats.completed_jobs}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Completed
                </Typography>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Typography variant="h6" color="info.main">
                  {stats.total_vessels_processed}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Vessels Processed
                </Typography>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Typography variant="body2" color="text.secondary">
                  Member since
                </Typography>
                <Typography variant="body2">
                  {new Date(stats.member_since).toLocaleDateString()}
                </Typography>
              </Grid>
            </Grid>
          )}
        </CardContent>
      </Card>

      {/* Profile Details */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Profile Information
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Display Name"
                value={formData.display_name}
                onChange={(e) => setFormData(prev => ({ ...prev, display_name: e.target.value }))}
                disabled={!editing}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Organization"
                value={formData.organization}
                onChange={(e) => setFormData(prev => ({ ...prev, organization: e.target.value }))}
                disabled={!editing}
              />
            </Grid>
          </Grid>

          <Divider sx={{ my: 3 }} />

          <Typography variant="h6" gutterBottom>
            Preferences
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth disabled={!editing}>
                <InputLabel>Theme</InputLabel>
                <Select
                  value={formData.preferences.theme}
                  label="Theme"
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    preferences: { ...prev.preferences, theme: e.target.value }
                  }))}
                >
                  <MenuItem value="light">Light</MenuItem>
                  <MenuItem value="dark">Dark</MenuItem>
                  <MenuItem value="auto">Auto</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth disabled={!editing}>
                <InputLabel>Default Export Format</InputLabel>
                <Select
                  value={formData.preferences.default_file_format}
                  label="Default Export Format"
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    preferences: { ...prev.preferences, default_file_format: e.target.value }
                  }))}
                >
                  <MenuItem value="csv">CSV</MenuItem>
                  <MenuItem value="json">JSON</MenuItem>
                  <MenuItem value="xlsx">Excel</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.preferences.notifications_enabled}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      preferences: { ...prev.preferences, notifications_enabled: e.target.checked }
                    }))}
                    disabled={!editing}
                  />
                }
                label="Enable Email Notifications"
              />
            </Grid>
          </Grid>

          {editing && (
            <Box sx={{ mt: 3, display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
              <Button
                variant="outlined"
                onClick={handleCancel}
                disabled={saving}
              >
                Cancel
              </Button>
              <Button
                variant="contained"
                startIcon={saving ? <CircularProgress size={20} /> : <Save />}
                onClick={handleSave}
                disabled={saving}
              >
                {saving ? 'Saving...' : 'Save Changes'}
              </Button>
            </Box>
          )}
        </CardContent>
      </Card>
    </Box>
  );
}
