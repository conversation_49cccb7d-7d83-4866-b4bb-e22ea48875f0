"""
Authentication Utilities
Firebase Auth integration for user verification
"""

import os
import json
import logging
from typing import Optional, Dict, Any
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import firebase_admin
from firebase_admin import auth, credentials
import base64

logger = logging.getLogger(__name__)

# Initialize Firebase Admin SDK
def initialize_firebase():
    """Initialize Firebase Admin SDK"""
    try:
        # Check if already initialized
        firebase_admin.get_app()
        logger.info("Firebase Admin SDK already initialized")
        return
    except ValueError:
        # Not initialized, proceed with initialization
        pass
    
    # Get credentials from environment
    project_id = os.getenv("FIREBASE_PROJECT_ID")
    credentials_json = os.getenv("FIREBASE_CREDENTIALS_JSON")
    
    if not project_id:
        development_mode = os.getenv('DEVELOPMENT_MODE', 'false').lower() == 'true'
        if development_mode:
            logger.warning("FIREBASE_PROJECT_ID not set, running in development mode - auth will be bypassed")
        else:
            logger.error("FIREBASE_PROJECT_ID not set and not in development mode, Firebase auth will not work")
        return
    
    if credentials_json:
        try:
            # Decode base64 encoded credentials
            if credentials_json.startswith('ey'):  # Looks like base64
                credentials_data = json.loads(base64.b64decode(credentials_json))
            else:
                credentials_data = json.loads(credentials_json)
            
            cred = credentials.Certificate(credentials_data)
            firebase_admin.initialize_app(cred, {
                'projectId': project_id
            })
            logger.info("Firebase Admin SDK initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Firebase Admin SDK: {e}")
            # Initialize with default credentials for development
            try:
                firebase_admin.initialize_app()
                logger.info("Firebase Admin SDK initialized with default credentials")
            except Exception as e2:
                logger.error(f"Failed to initialize Firebase with defaults: {e2}")
    else:
        logger.warning("FIREBASE_CREDENTIALS_JSON not set, Firebase auth may not work in production")

# Initialize on import
initialize_firebase()

# Security scheme
security = HTTPBearer(auto_error=False)

async def verify_firebase_token(credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)) -> Optional[Dict[str, Any]]:
    """
    Verify Firebase ID token and return user info
    Returns None if no token provided or verification fails
    """
    # Check if in development mode
    development_mode = os.getenv('DEVELOPMENT_MODE', 'false').lower() == 'true'
    allow_anonymous = os.getenv('ALLOW_ANONYMOUS_ACCESS', 'false').lower() == 'true'

    if development_mode and allow_anonymous:
        # Return a mock user for development
        return {
            'uid': 'dev-user-123',
            'email': '<EMAIL>',
            'name': 'Development User',
            'admin': True
        }

    if not credentials:
        return None
    
    try:
        # Extract token from Authorization header
        token = credentials.credentials
        
        # Verify the token with Firebase
        decoded_token = auth.verify_id_token(token)
        
        logger.info(f"Token verified for user: {decoded_token.get('uid')}")
        return decoded_token
        
    except auth.InvalidIdTokenError:
        logger.warning("Invalid Firebase ID token provided")
        raise HTTPException(status_code=401, detail="Invalid authentication token")
    except auth.ExpiredIdTokenError:
        logger.warning("Expired Firebase ID token provided")
        raise HTTPException(status_code=401, detail="Authentication token has expired")
    except auth.RevokedIdTokenError:
        logger.warning("Revoked Firebase ID token provided")
        raise HTTPException(status_code=401, detail="Authentication token has been revoked")
    except Exception as e:
        logger.error(f"Token verification failed: {e}")
        raise HTTPException(status_code=401, detail="Authentication failed")

async def get_current_user(token_data: Optional[Dict[str, Any]] = Depends(verify_firebase_token)) -> str:
    """
    Get current user ID from verified token
    Raises 401 if no valid token provided
    """
    if not token_data:
        raise HTTPException(
            status_code=401,
            detail="Authentication required"
        )
    
    user_id = token_data.get('uid')
    if not user_id:
        raise HTTPException(
            status_code=401,
            detail="Invalid token: missing user ID"
        )
    
    return user_id

async def get_current_user_optional(token_data: Optional[Dict[str, Any]] = Depends(verify_firebase_token)) -> Optional[str]:
    """
    Get current user ID if token provided, otherwise return None
    Does not raise exception for missing auth
    """
    if not token_data:
        return None
    
    return token_data.get('uid')

async def verify_admin_user(token_data: Optional[Dict[str, Any]] = Depends(verify_firebase_token)) -> str:
    """
    Verify user is an admin
    Raises 403 if user is not admin
    """
    if not token_data:
        raise HTTPException(status_code=401, detail="Authentication required")
    
    user_id = token_data.get('uid')
    if not user_id:
        raise HTTPException(status_code=401, detail="Invalid token")
    
    # Check admin claim
    is_admin = token_data.get('admin', False)
    if not is_admin:
        # Also check custom claims
        try:
            user_record = auth.get_user(user_id)
            custom_claims = user_record.custom_claims or {}
            is_admin = custom_claims.get('admin', False)
        except Exception as e:
            logger.error(f"Failed to get user claims: {e}")
            raise HTTPException(status_code=403, detail="Access denied")
    
    if not is_admin:
        logger.warning(f"Non-admin user {user_id} attempted admin access")
        raise HTTPException(status_code=403, detail="Admin access required")
    
    return user_id

def get_user_info(user_id: str) -> Optional[Dict[str, Any]]:
    """
    Get user information from Firebase
    """
    try:
        user_record = auth.get_user(user_id)
        return {
            'uid': user_record.uid,
            'email': user_record.email,
            'email_verified': user_record.email_verified,
            'display_name': user_record.display_name,
            'photo_url': user_record.photo_url,
            'disabled': user_record.disabled,
            'custom_claims': user_record.custom_claims,
            'creation_timestamp': user_record.user_metadata.creation_timestamp,
            'last_sign_in_timestamp': user_record.user_metadata.last_sign_in_timestamp
        }
    except Exception as e:
        logger.error(f"Failed to get user info for {user_id}: {e}")
        return None

def set_custom_claims(user_id: str, claims: Dict[str, Any]) -> bool:
    """
    Set custom claims for a user
    """
    try:
        auth.set_custom_user_claims(user_id, claims)
        logger.info(f"Custom claims set for user {user_id}: {claims}")
        return True
    except Exception as e:
        logger.error(f"Failed to set custom claims for {user_id}: {e}")
        return False

def revoke_user_tokens(user_id: str) -> bool:
    """
    Revoke all tokens for a user
    """
    try:
        auth.revoke_refresh_tokens(user_id)
        logger.info(f"Tokens revoked for user {user_id}")
        return True
    except Exception as e:
        logger.error(f"Failed to revoke tokens for {user_id}: {e}")
        return False

async def create_custom_token(user_id: str, additional_claims: Optional[Dict[str, Any]] = None) -> Optional[str]:
    """
    Create custom token for a user (useful for server-to-server auth)
    """
    try:
        custom_token = auth.create_custom_token(user_id, additional_claims)
        return custom_token.decode('utf-8')
    except Exception as e:
        logger.error(f"Failed to create custom token for {user_id}: {e}")
        return None

# Security Note: Development authentication bypass code has been removed
# to eliminate potential security vulnerabilities in production environments.
# All authentication now goes through Firebase Auth for maximum security.