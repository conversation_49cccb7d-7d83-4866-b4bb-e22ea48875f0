#!/usr/bin/env python3
"""
Comprehensive Integration Test Suite for VESLINT
Tests all database structures, backend APIs, and their professional integration
"""

import asyncio
import os
import sys
import json
import logging
from pathlib import Path
from typing import Dict, Any, List

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_database_integration():
    """Test complete database structure and integration"""
    print("🔍 Testing Database Integration...")
    
    try:
        from src.database.init import get_database_manager
        from src.database.schemas import DatabaseSchemas, get_migration_sql
        
        # Test database manager initialization
        db_manager = await get_database_manager()
        health = await db_manager.health_check()
        
        if health.get("status") != "healthy":
            print(f"❌ Database health check failed: {health}")
            return False
        
        # Test schema validation
        schemas = DatabaseSchemas()
        required_schemas = ['jobs', 'vessels', 'user_profiles', 'job_analytics']
        
        for schema_name in required_schemas:
            schema = getattr(schemas, f"{schema_name}_schema", None)
            if not schema:
                print(f"❌ Missing schema: {schema_name}")
                return False
        
        print("✅ Database integration successful")
        return True
        
    except Exception as e:
        print(f"❌ Database integration failed: {e}")
        return False

async def test_api_endpoints():
    """Test all API endpoints are properly configured"""
    print("\n🔍 Testing API Endpoints...")
    
    try:
        from src.api.routes import ROUTE_GROUPS, validate_route_configuration
        
        # Validate route configuration
        if not validate_route_configuration():
            print("❌ Route configuration validation failed")
            return False
        
        # Check all expected route groups exist
        expected_groups = ['health', 'jobs', 'vessels', 'users']
        found_groups = [group['prefix'].strip('/') for group in ROUTE_GROUPS]
        
        missing_groups = [group for group in expected_groups if group not in found_groups]
        if missing_groups:
            print(f"❌ Missing route groups: {missing_groups}")
            return False
        
        # Test route group structure
        for group in ROUTE_GROUPS:
            router = group['router']
            if not hasattr(router, 'routes') or len(router.routes) == 0:
                print(f"❌ Route group {group['prefix']} has no routes")
                return False
        
        print("✅ API endpoints properly configured")
        return True
        
    except Exception as e:
        print(f"❌ API endpoint test failed: {e}")
        return False

async def test_model_integration():
    """Test Pydantic models integration with database"""
    print("\n🔍 Testing Model Integration...")
    
    try:
        from src.models import MODEL_REGISTRY, MODEL_CATEGORIES
        from src.models.job import JobResponse, JobCreate
        from src.models.vessel import VesselClassificationResponse
        from src.models.user import UserProfileResponse
        
        # Test model registry completeness
        expected_categories = ['job', 'vessel', 'user', 'response']
        missing_categories = [cat for cat in expected_categories if cat not in MODEL_CATEGORIES]
        
        if missing_categories:
            print(f"❌ Missing model categories: {missing_categories}")
            return False
        
        # Test key models can be instantiated
        test_models = [
            ('JobResponse', {
                'id': 'test-id',
                'user_id': 'test-user',
                'filename': 'test.csv',
                'status': 'created',
                'created_at': '2024-01-01T00:00:00Z'
            }),
            ('UserProfileResponse', {
                'id': 'test-id',
                'user_id': 'test-user',
                'email': '<EMAIL>',
                'role': 'user',
                'created_at': '2024-01-01T00:00:00Z'
            })
        ]
        
        for model_name, test_data in test_models:
            if model_name not in MODEL_REGISTRY:
                print(f"❌ Model {model_name} not in registry")
                return False
            
            try:
                model_class = MODEL_REGISTRY[model_name]
                instance = model_class(**test_data)
                print(f"✅ Model {model_name} instantiated successfully")
            except Exception as e:
                print(f"❌ Failed to instantiate {model_name}: {e}")
                return False
        
        print("✅ Model integration successful")
        return True
        
    except Exception as e:
        print(f"❌ Model integration test failed: {e}")
        return False

async def test_service_integration():
    """Test service layer integration"""
    print("\n🔍 Testing Service Integration...")
    
    try:
        from src.services.ml_service import VesselClassifier
        from src.services.csv_processor import CSVProcessor
        from src.services.feature_engineering import FeatureEngineer
        
        # Test ML service
        classifier = VesselClassifier()
        await classifier.initialize()
        
        if not classifier.model_loaded:
            print("❌ ML service failed to initialize")
            return False
        
        # Test CSV processor
        processor = CSVProcessor()
        if not hasattr(processor, 'process_csv'):
            print("❌ CSV processor missing process_csv method")
            return False
        
        # Test feature engineer
        engineer = FeatureEngineer()
        if not hasattr(engineer, 'extract_features'):
            print("❌ Feature engineer missing extract_features method")
            return False
        
        print("✅ Service integration successful")
        return True
        
    except Exception as e:
        print(f"❌ Service integration test failed: {e}")
        return False

async def test_authentication_integration():
    """Test authentication system integration"""
    print("\n🔍 Testing Authentication Integration...")
    
    try:
        from src.utils.auth import verify_firebase_token, get_current_user
        
        # Test development mode authentication
        os.environ['DEVELOPMENT_MODE'] = 'true'
        os.environ['ALLOW_ANONYMOUS_ACCESS'] = 'true'
        
        # Test auth function exists and works in dev mode
        mock_user = await verify_firebase_token(None)
        
        if not mock_user or mock_user.get('uid') != 'dev-user-123':
            print("❌ Development mode authentication failed")
            return False
        
        print("✅ Authentication integration successful")
        return True
        
    except Exception as e:
        print(f"❌ Authentication integration test failed: {e}")
        return False

async def test_database_operations():
    """Test database CRUD operations"""
    print("\n🔍 Testing Database Operations...")
    
    try:
        from src.database.init import (
            create_job, get_job_by_id, update_job,
            create_vessel_classification, get_job_classifications
        )
        
        # Test job operations
        test_job_data = {
            'user_id': 'test-user-123',
            'filename': 'test.csv',
            'status': 'created',
            'total_vessels': 10
        }
        
        # Note: This would require a test database
        # For now, just verify the functions exist and are callable
        
        required_functions = [
            create_job, get_job_by_id, update_job,
            create_vessel_classification, get_job_classifications
        ]
        
        for func in required_functions:
            if not callable(func):
                print(f"❌ Database function {func.__name__} is not callable")
                return False
        
        print("✅ Database operations properly defined")
        return True
        
    except Exception as e:
        print(f"❌ Database operations test failed: {e}")
        return False

async def test_error_handling():
    """Test error handling throughout the system"""
    print("\n🔍 Testing Error Handling...")
    
    try:
        from src.models.response import ErrorResponse, APIResponse
        from src.utils.validation import validate_csv_file
        
        # Test error response models
        error_response = ErrorResponse(
            success=False,
            message="Test error",
            error_code="TEST_ERROR"
        )
        
        if not error_response.success:
            print("✅ Error response model works correctly")
        else:
            print("❌ Error response model failed")
            return False
        
        # Test API response wrapper
        api_response = APIResponse(
            success=True,
            data={"test": "data"},
            message="Success"
        )
        
        if api_response.success and api_response.data:
            print("✅ API response model works correctly")
        else:
            print("❌ API response model failed")
            return False
        
        print("✅ Error handling integration successful")
        return True
        
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False

async def run_integration_tests():
    """Run all integration tests"""
    print("🚀 Starting VESLINT Integration Test Suite\n")
    
    tests = [
        ("Database Integration", test_database_integration),
        ("API Endpoints", test_api_endpoints),
        ("Model Integration", test_model_integration),
        ("Service Integration", test_service_integration),
        ("Authentication Integration", test_authentication_integration),
        ("Database Operations", test_database_operations),
        ("Error Handling", test_error_handling),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*60)
    print("📊 INTEGRATION TEST RESULTS SUMMARY")
    print("="*60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:25} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All integration tests passed! System is professionally integrated.")
        return True
    else:
        print("⚠️  Some integration tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(level=logging.WARNING)  # Reduce noise
    
    # Run integration tests
    success = asyncio.run(run_integration_tests())
    sys.exit(0 if success else 1)
