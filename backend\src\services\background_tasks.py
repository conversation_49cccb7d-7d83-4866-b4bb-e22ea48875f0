"""
Background Tasks Service
Handles async vessel classification processing
"""

import logging
import asyncio
from typing import Dict, Any, List
import pandas as pd
from datetime import datetime

from .csv_processor import process_uploaded_csv
from .ml_service import get_classifier
from ..database.init import (
    update_job, get_job_by_id as get_job, create_job
)
from ..database.supabase_client import get_supabase_client
from ..utils.constants import JOB_STATUS, PROCESSING_CONFIG

logger = logging.getLogger(__name__)

async def start_vessel_classification(job_id: str, file_path: str, user_id: str):
    """
    Start vessel classification background task
    
    Args:
        job_id: Job identifier
        file_path: Path to uploaded file in storage
        user_id: User identifier
    """
    try:
        logger.info(f"Starting vessel classification job {job_id}")
        
        # Update job status to processing
        await update_job(job_id, {
            'status': JOB_STATUS['PROCESSING'],
            'started_at': datetime.utcnow().isoformat()
        })
        
        # Download file from storage
        logger.info(f"Downloading file from storage: {file_path}")
        supabase_client = await get_supabase_client()
        file_content = await supabase_client.download_file('ais-data', file_path)
        
        # Process CSV file
        logger.info(f"Processing CSV file for job {job_id}")
        await update_job(job_id, {
            'status': JOB_STATUS['EXTRACTING_FEATURES']
        })
        
        processing_result = await process_uploaded_csv(file_content, file_path.split('/')[-1])
        
        if not processing_result['success']:
            raise Exception(f"CSV processing failed: {processing_result.get('error', 'Unknown error')}")
        
        # Update job with vessel count
        await update_job(job_id, {
            'total_vessels': processing_result['vessel_count']
        })
        
        # Convert to DataFrame for ML processing
        vessel_records = []
        for vessel in processing_result['vessels']:
            # We need to reconstruct the AIS data
            # In a real implementation, you'd store this data temporarily
            # For now, we'll get it from the original CSV
            pass
        
        # Re-parse the CSV for ML processing
        import io
        csv_text = file_content.decode('utf-8')
        df = pd.read_csv(io.StringIO(csv_text))
        
        # Clean the data
        from .csv_processor import clean_ais_data
        cleaned_df = await clean_ais_data(df)
        
        # Process in chunks for large datasets
        vessels_per_chunk = PROCESSING_CONFIG['chunk_size']
        unique_vessels = cleaned_df['mmsi'].unique()
        
        if len(unique_vessels) <= vessels_per_chunk:
            # Process directly for small datasets
            await process_single_chunk(job_id, cleaned_df, unique_vessels)
        else:
            # Process in chunks for large datasets
            await process_in_chunks(job_id, cleaned_df, unique_vessels, vessels_per_chunk)
        
        # Mark job as completed
        await update_job(job_id, {
            'status': JOB_STATUS['COMPLETED'],
            'completed_at': datetime.utcnow().isoformat(),
            'progress': 100
        })
        
        logger.info(f"Job {job_id} completed successfully")
        
    except Exception as e:
        logger.error(f"Job {job_id} failed: {e}")
        
        # Update job with error status
        await update_job(job_id, {
            'status': JOB_STATUS['FAILED'],
            'error_message': str(e),
            'completed_at': datetime.utcnow().isoformat()
        })

async def process_single_chunk(job_id: str, df: pd.DataFrame, vessel_mmsis: List[str]):
    """
    Process a single chunk of vessels
    
    Args:
        job_id: Job identifier
        df: AIS DataFrame
        vessel_mmsis: List of vessel MMSIs to process
    """
    try:
        logger.info(f"Processing single chunk for job {job_id}: {len(vessel_mmsis)} vessels")
        
        # Update status
        await update_job(job_id, {
            'status': JOB_STATUS['CLASSIFYING']
        })
        
        # Filter data for these vessels
        chunk_df = df[df['mmsi'].isin(vessel_mmsis)]
        
        # Get classifier and classify vessels
        classifier = await get_classifier()
        results = await classifier.classify_vessels(chunk_df)
        
        # Store results in database
        await store_classification_results(job_id, results)
        
        # Update progress
        await update_job(job_id, {
            'processed_vessels': len(vessel_mmsis),
            'progress': 100
        })
        
        logger.info(f"Single chunk processing completed for job {job_id}")
        
    except Exception as e:
        logger.error(f"Single chunk processing failed for job {job_id}: {e}")
        raise

async def process_in_chunks(job_id: str, df: pd.DataFrame, vessel_mmsis: List[str], chunk_size: int):
    """
    Process vessels in multiple chunks
    
    Args:
        job_id: Job identifier
        df: AIS DataFrame
        vessel_mmsis: List of all vessel MMSIs
        chunk_size: Number of vessels per chunk
    """
    try:
        logger.info(f"Processing in chunks for job {job_id}: {len(vessel_mmsis)} vessels, {chunk_size} per chunk")
        
        total_vessels = len(vessel_mmsis)
        processed_vessels = 0
        
        # Process chunks sequentially to avoid memory issues
        for i in range(0, total_vessels, chunk_size):
            chunk_vessels = vessel_mmsis[i:i + chunk_size]
            chunk_number = (i // chunk_size) + 1
            total_chunks = (total_vessels + chunk_size - 1) // chunk_size
            
            logger.info(f"Processing chunk {chunk_number}/{total_chunks} for job {job_id}")
            
            # Update status
            await update_job(job_id, {
                'status': JOB_STATUS['CLASSIFYING']
            })
            
            # Filter data for this chunk
            chunk_df = df[df['mmsi'].isin(chunk_vessels)]
            
            # Classify vessels in this chunk
            classifier = await get_classifier()
            results = await classifier.classify_vessels(chunk_df)
            
            # Store results
            await store_classification_results(job_id, results)
            
            # Update progress
            processed_vessels += len(chunk_vessels)
            progress = int((processed_vessels / total_vessels) * 100)
            
            await update_job(job_id, {
                'processed_vessels': processed_vessels,
                'progress': progress
            })
            
            logger.info(f"Completed chunk {chunk_number}/{total_chunks} for job {job_id}")
            
            # Small delay to prevent overwhelming the system
            await asyncio.sleep(0.1)
        
        logger.info(f"Chunk processing completed for job {job_id}")
        
    except Exception as e:
        logger.error(f"Chunk processing failed for job {job_id}: {e}")
        raise

async def store_classification_results(job_id: str, results: List[Dict[str, Any]]):
    """
    Store classification results in database
    
    Args:
        job_id: Job identifier
        results: List of classification results
    """
    try:
        logger.info(f"Storing {len(results)} classification results for job {job_id}")
        
        # Prepare records for batch insert (updated for new schema)
        vessel_classifications = []
        for result in results:
            # Convert MMSI to integer if it's a string
            mmsi_value = result['mmsi']
            if isinstance(mmsi_value, str):
                try:
                    mmsi_value = int(mmsi_value)
                except ValueError:
                    logger.warning(f"Invalid MMSI format: {mmsi_value}")
                    continue

            classification = {
                'job_id': job_id,
                'mmsi': mmsi_value,
                'predicted_type': result.get('class_name', result.get('predicted_type')),
                'confidence': result.get('confidence'),
                'class_probabilities': result.get('class_probabilities'),
                'features': result.get('features'),
                'raw_data': result.get('raw_data'),
                'data_points': result.get('data_points'),
                'time_span_hours': result.get('time_span_hours'),
                'first_seen': result.get('first_seen'),
                'last_seen': result.get('last_seen')
            }

            # Remove None values to use database defaults
            classification = {k: v for k, v in classification.items() if v is not None}

            vessel_classifications.append(classification)
        
        # Batch insert into database
        if vessel_classifications:
            supabase_client = await get_supabase_client()
            result = await supabase_client.insert("vessel_classifications", vessel_classifications)
            logger.info(f"Stored {len(vessel_classifications)} classification results")
        
    except Exception as e:
        logger.error(f"Failed to store classification results for job {job_id}: {e}")
        raise

async def cleanup_old_jobs():
    """
    Cleanup old completed jobs to save storage space
    Background task that runs periodically
    """
    try:
        logger.info("Starting cleanup of old jobs")
        
        # Get jobs older than cleanup threshold
        cleanup_hours = PROCESSING_CONFIG['cleanup_after_hours']
        cutoff_time = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
        cutoff_time = cutoff_time.timestamp() - (cleanup_hours * 3600)
        
        # Query old completed/failed jobs
        supabase_client = await get_supabase_client()

        # Get all jobs (we'll filter in Python for now)
        # TODO: Enhance SupabaseManager to support complex queries
        all_jobs = await supabase_client.select("jobs", "id, file_path, status, created_at")

        # Filter old completed/failed jobs in Python
        cutoff_datetime = datetime.fromtimestamp(cutoff_time)
        old_jobs_data = []
        for job in all_jobs:
            if (job.get('status') in [JOB_STATUS['COMPLETED'], JOB_STATUS['FAILED']] and
                job.get('created_at') and
                datetime.fromisoformat(job['created_at'].replace('Z', '+00:00')) < cutoff_datetime):
                old_jobs_data.append(job)

        if not old_jobs_data:
            logger.info("No old jobs to cleanup")
            return

        logger.info(f"Found {len(old_jobs_data)} old jobs to cleanup")
        
        # Cleanup each job
        for job in old_jobs_data:
            try:
                job_id = job['id']
                file_path = job.get('file_path')

                # Delete classification results
                await supabase_client.delete("vessel_classifications", {"job_id": job_id})

                # Delete file from storage if exists
                if file_path:
                    try:
                        await supabase_client.delete_file('ais-data', file_path)
                    except Exception as e:
                        logger.warning(f"Failed to delete file {file_path}: {e}")

                # Delete job record
                await supabase_client.delete("jobs", {"id": job_id})

                logger.info(f"Cleaned up job {job_id}")
                
            except Exception as e:
                logger.error(f"Failed to cleanup job {job['id']}: {e}")
        
        logger.info(f"Cleanup completed: processed {len(old_jobs.data)} jobs")
        
    except Exception as e:
        logger.error(f"Job cleanup failed: {e}")

async def reprocess_failed_job(job_id: str):
    """
    Reprocess a failed job
    
    Args:
        job_id: Job identifier to reprocess
    """
    try:
        logger.info(f"Reprocessing failed job {job_id}")
        
        # Get job details
        job = await get_job(job_id)
        if not job:
            raise ValueError(f"Job {job_id} not found")
        
        if job['status'] != JOB_STATUS['FAILED']:
            raise ValueError(f"Job {job_id} is not in failed status")
        
        # Reset job status
        await update_job(job_id, {
            'status': JOB_STATUS['CREATED'],
            'error_message': None,
            'progress': 0,
            'processed_vessels': 0
        })
        
        # Start processing again
        await start_vessel_classification(job_id, job['file_path'], job['user_id'])
        
        logger.info(f"Job {job_id} reprocessing started")
        
    except Exception as e:
        logger.error(f"Failed to reprocess job {job_id}: {e}")
        raise

async def cancel_job(job_id: str):
    """
    Cancel a running job
    
    Args:
        job_id: Job identifier to cancel
    """
    try:
        logger.info(f"Cancelling job {job_id}")
        
        # Update job status
        await update_job(job_id, {
            'status': JOB_STATUS['CANCELLED'],
            'completed_at': datetime.utcnow().isoformat()
        })
        
        # In a production system with a proper task queue (like Celery),
        # you would also cancel the actual background task here
        
        logger.info(f"Job {job_id} cancelled")
        
    except Exception as e:
        logger.error(f"Failed to cancel job {job_id}: {e}")
        raise

async def get_job_progress(job_id: str) -> Dict[str, Any]:
    """
    Get detailed job progress information
    
    Args:
        job_id: Job identifier
        
    Returns:
        Detailed progress information
    """
    try:
        job = await get_job(job_id)
        if not job:
            raise ValueError(f"Job {job_id} not found")
        
        # Calculate progress metrics
        progress_info = {
            'job_id': job_id,
            'status': job['status'],
            'progress': job['progress'],
            'total_vessels': job['total_vessels'],
            'processed_vessels': job['processed_vessels'],
            'created_at': job['created_at'],
            'started_at': job.get('started_at'),
            'updated_at': job['updated_at'],
            'estimated_completion': None,
            'processing_rate': None
        }
        
        # Calculate processing rate and estimated completion
        if job['status'] in [JOB_STATUS['PROCESSING'], JOB_STATUS['CLASSIFYING']]:
            if job.get('started_at') and job['processed_vessels'] > 0:
                start_time = datetime.fromisoformat(job['started_at'].replace('Z', '+00:00'))
                elapsed_seconds = (datetime.utcnow().replace(tzinfo=start_time.tzinfo) - start_time).total_seconds()
                
                if elapsed_seconds > 0:
                    processing_rate = job['processed_vessels'] / elapsed_seconds  # vessels per second
                    progress_info['processing_rate'] = processing_rate * 60  # vessels per minute
                    
                    # Estimate completion time
                    remaining_vessels = job['total_vessels'] - job['processed_vessels']
                    if processing_rate > 0:
                        estimated_seconds = remaining_vessels / processing_rate
                        progress_info['estimated_completion_seconds'] = int(estimated_seconds)
        
        return progress_info
        
    except Exception as e:
        logger.error(f"Failed to get job progress for {job_id}: {e}")
        raise

# Task scheduler (would be replaced by Celery or similar in production)
class SimpleTaskScheduler:
    """Simple task scheduler for background jobs"""
    
    def __init__(self):
        self.running_tasks = {}
    
    async def schedule_job(self, job_id: str, file_path: str, user_id: str):
        """Schedule a new classification job"""
        if job_id in self.running_tasks:
            logger.warning(f"Job {job_id} is already scheduled")
            return
        
        # Create task
        task = asyncio.create_task(
            start_vessel_classification(job_id, file_path, user_id)
        )
        self.running_tasks[job_id] = task
        
        # Clean up task when done
        task.add_done_callback(lambda t: self.running_tasks.pop(job_id, None))
        
        logger.info(f"Scheduled job {job_id}")
    
    async def cancel_job(self, job_id: str):
        """Cancel a scheduled job"""
        if job_id in self.running_tasks:
            task = self.running_tasks[job_id]
            task.cancel()
            del self.running_tasks[job_id]
            logger.info(f"Cancelled scheduled job {job_id}")
    
    def get_running_jobs(self) -> List[str]:
        """Get list of currently running job IDs"""
        return list(self.running_tasks.keys())

# Global task scheduler instance
task_scheduler = SimpleTaskScheduler()