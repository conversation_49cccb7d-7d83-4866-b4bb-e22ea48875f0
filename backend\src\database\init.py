"""
Database Module Initialization for VESLINT Backend.

This module provides centralized database connectivity and management for the
VESLINT maritime vessel classification system using Supabase.
"""

import os
import logging
import asyncio
from typing import Dict, Any, Optional, List
from contextlib import asynccontextmanager
from dataclasses import dataclass

from .supabase_client import SupabaseManager, get_supabase_client
from .schemas import DatabaseSchemas, validate_schema

# Module-level logger
logger = logging.getLogger(__name__)

# =============================================================================
# Database Configuration
# =============================================================================

@dataclass
class DatabaseConfig:
    """Database configuration settings."""
    
    supabase_url: str
    supabase_key: str
    supabase_service_role_key: str
    pool_size: int = 10
    max_overflow: int = 20
    pool_timeout: int = 30
    connection_timeout: int = 10
    enable_realtime: bool = True
    enable_storage: bool = True
    
    @classmethod
    def from_env(cls) -> 'DatabaseConfig':
        """Create config from environment variables."""
        return cls(
            supabase_url=os.getenv('SUPABASE_URL', ''),
            supabase_key=os.getenv('SUPABASE_ANON_KEY', ''),
            supabase_service_role_key=os.getenv('SUPABASE_SERVICE_ROLE_KEY', ''),
            pool_size=int(os.getenv('DB_POOL_SIZE', '10')),
            max_overflow=int(os.getenv('DB_MAX_OVERFLOW', '20')),
            pool_timeout=int(os.getenv('DB_POOL_TIMEOUT', '30')),
            connection_timeout=int(os.getenv('DB_CONNECTION_TIMEOUT', '10')),
            enable_realtime=os.getenv('ENABLE_REALTIME', 'true').lower() == 'true',
            enable_storage=os.getenv('ENABLE_STORAGE', 'true').lower() == 'true'
        )

# =============================================================================
# Database Manager
# =============================================================================

class DatabaseManager:
    """Centralized database management for VESLINT."""
    
    def __init__(self, config: Optional[DatabaseConfig] = None):
        """Initialize database manager."""
        self.config = config or DatabaseConfig.from_env()
        self.supabase_manager: Optional[SupabaseManager] = None
        self.schemas = DatabaseSchemas()
        self._initialized = False
        
    async def initialize(self) -> None:
        """Initialize database connections and validate schemas."""
        if self._initialized:
            logger.warning("Database manager already initialized")
            return
            
        try:
            logger.info("Initializing database manager...")
            
            # Initialize Supabase manager
            self.supabase_manager = SupabaseManager(
                url=self.config.supabase_url,
                key=self.config.supabase_key,
                service_role_key=self.config.supabase_service_role_key
            )
            
            await self.supabase_manager.initialize()
            
            # Validate database schemas
            await self._validate_schemas()
            
            # Setup real-time subscriptions if enabled
            if self.config.enable_realtime:
                await self._setup_realtime()
                
            self._initialized = True
            logger.info("Database manager initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize database manager: {e}")
            raise
    
    async def cleanup(self) -> None:
        """Cleanup database connections."""
        if not self._initialized:
            return
            
        try:
            logger.info("Cleaning up database connections...")
            
            if self.supabase_manager:
                await self.supabase_manager.cleanup()
                
            self._initialized = False
            logger.info("Database cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during database cleanup: {e}")
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform database health check."""
        if not self._initialized:
            return {"status": "error", "message": "Database not initialized"}
            
        try:
            # Test Supabase connection
            supabase_health = await self.supabase_manager.health_check()
            
            return {
                "status": "healthy",
                "supabase": supabase_health,
                "schemas_valid": await self._validate_schemas(),
                "timestamp": "2024-01-01T00:00:00Z"
            }
            
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return {
                "status": "error",
                "message": str(e),
                "timestamp": "2024-01-01T00:00:00Z"
            }
    
    async def _validate_schemas(self) -> bool:
        """Validate database schemas."""
        try:
            if not self.supabase_manager:
                return False
                
            # Check if required tables exist
            required_tables = ['jobs', 'vessels', 'user_profiles']
            
            for table in required_tables:
                exists = await self.supabase_manager.table_exists(table)
                if not exists:
                    logger.warning(f"Required table '{table}' does not exist")
                    return False
                    
            logger.info("All required database schemas validated")
            return True
            
        except Exception as e:
            logger.error(f"Schema validation failed: {e}")
            return False
    
    async def _setup_realtime(self) -> None:
        """Setup real-time subscriptions for job monitoring."""
        if not self.supabase_manager:
            return
            
        try:
            # Setup job status real-time subscription
            await self.supabase_manager.setup_realtime_subscription(
                'jobs',
                ['status', 'progress', 'results']
            )
            
            logger.info("Real-time subscriptions configured")
            
        except Exception as e:
            logger.error(f"Failed to setup real-time subscriptions: {e}")

# =============================================================================
# Global Database Instance
# =============================================================================

# Global database manager instance
_db_manager: Optional[DatabaseManager] = None

async def get_database_manager() -> DatabaseManager:
    """Get or create the global database manager instance."""
    global _db_manager
    
    if _db_manager is None:
        _db_manager = DatabaseManager()
        await _db_manager.initialize()
        
    return _db_manager

@asynccontextmanager
async def database_session():
    """Context manager for database sessions."""
    db_manager = await get_database_manager()
    
    try:
        yield db_manager
    except Exception as e:
        logger.error(f"Database session error: {e}")
        raise
    finally:
        # Cleanup if needed
        pass

# =============================================================================
# Convenience Functions
# =============================================================================

async def get_supabase() -> SupabaseManager:
    """Get Supabase client instance."""
    db_manager = await get_database_manager()
    if not db_manager.supabase_manager:
        raise RuntimeError("Supabase manager not initialized")
    return db_manager.supabase_manager

async def execute_query(table: str, operation: str, data: Optional[Dict] = None, 
                       filters: Optional[Dict] = None) -> Any:
    """Execute a database query with error handling."""
    try:
        supabase = await get_supabase()
        return await supabase.execute_query(table, operation, data, filters)
    except Exception as e:
        logger.error(f"Query execution failed: {e}")
        raise

async def get_job_by_id(job_id: str) -> Optional[Dict]:
    """Get job by ID."""
    return await execute_query('jobs', 'select', filters={'id': job_id})

async def update_job_status(job_id: str, status: str, progress: int = None, 
                           results: Dict = None) -> bool:
    """Update job status."""
    update_data = {'status': status}
    if progress is not None:
        update_data['progress'] = progress
    if results is not None:
        update_data['results'] = results
        
    result = await execute_query('jobs', 'update', update_data, {'id': job_id})
    return result is not None

async def create_job(job_data: Dict) -> Optional[str]:
    """Create a new job."""
    result = await execute_query('jobs', 'insert', job_data)
    return result.get('id') if result else None

async def get_user_jobs(user_id: str, limit: int = 50) -> List[Dict]:
    """Get jobs for a user."""
    result = await execute_query('jobs', 'select',
                                filters={'user_id': user_id, 'limit': limit})
    return result if isinstance(result, list) else []

async def update_job(job_id: str, update_data: Dict) -> Optional[Dict]:
    """Update a job with arbitrary data."""
    result = await execute_query('jobs', 'update', update_data, {'id': job_id})
    return result

async def create_vessel_classification(classification_data: Dict) -> Optional[str]:
    """Create a new vessel classification record."""
    try:
        result = await execute_query('vessel_classifications', 'insert', classification_data)
        return result.get('id') if result else None
    except Exception as e:
        logger.error(f"Failed to create vessel classification: {e}")
        return None

async def get_job_classifications(job_id: str) -> List[Dict]:
    """Get all vessel classifications for a job."""
    try:
        supabase = await get_supabase()
        return await supabase.select(
            'vessel_classifications',
            '*',
            filters={'job_id': job_id},
            order_by='created_at ASC'
        )
    except Exception as e:
        logger.error(f"Failed to get job classifications: {e}")
        return []

async def get_vessel_classification(job_id: str, mmsi: int) -> Optional[Dict]:
    """Get vessel classification by job_id and mmsi."""
    try:
        supabase = await get_supabase()
        results = await supabase.select(
            'vessel_classifications',
            '*',
            filters={'job_id': job_id, 'mmsi': mmsi}
        )
        return results[0] if results else None
    except Exception as e:
        logger.error(f"Failed to get vessel classification: {e}")
        return None

async def delete_job_classifications(job_id: str) -> bool:
    """Delete all vessel classifications for a job."""
    try:
        result = await execute_query(
            'vessel_classifications',
            'delete',
            filters={'job_id': job_id}
        )
        return True
    except Exception as e:
        logger.error(f"Failed to delete job classifications: {e}")
        return False

# =============================================================================
# Database Utilities
# =============================================================================

def validate_connection_string(url: str) -> bool:
    """Validate database connection string format."""
    return url.startswith('https://') and '.supabase.co' in url

def get_table_schema(table_name: str) -> Optional[Dict]:
    """Get schema definition for a table."""
    schemas = DatabaseSchemas()
    return getattr(schemas, f"{table_name}_schema", None)

async def migrate_database() -> bool:
    """Run database migrations."""
    try:
        logger.info("Running database migrations...")
        # Migration logic would go here
        # This would typically run SQL migration files
        logger.info("Database migrations completed successfully")
        return True
    except Exception as e:
        logger.error(f"Database migration failed: {e}")
        return False

# =============================================================================
# Exports
# =============================================================================

__all__ = [
    # Main classes
    'DatabaseConfig',
    'DatabaseManager',
    
    # Factory functions
    'get_database_manager',
    'get_supabase',
    'database_session',
    
    # Convenience functions
    'execute_query',
    'get_job_by_id',
    'update_job_status',
    'update_job',
    'create_job',
    'get_user_jobs',

    # Vessel classification functions
    'create_vessel_classification',
    'get_job_classifications',
    'get_vessel_classification',
    'delete_job_classifications',
    
    # Utilities
    'validate_connection_string',
    'get_table_schema',
    'migrate_database',
    
    # Schemas
    'DatabaseSchemas',
    'validate_schema'
]