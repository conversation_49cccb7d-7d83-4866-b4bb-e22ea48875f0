"use client";

import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { useAuth } from './useAuth';

export function useSupabase() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // Set up Supabase auth state listener
  useEffect(() => {
    if (user) {
      // When user is authenticated, we can set up Supabase session
      // This is a placeholder for actual implementation
      // In a real app, you might sync Firebase auth with Supabase
    }
  }, [user]);

  /**
   * Generic function to handle Supabase queries with error handling
   */
  const query = async <T>(
    callback: () => Promise<{ data: T; error: Error | null }>
  ): Promise<T | null> => {
    setLoading(true);
    setError(null);
    try {
      const { data, error } = await callback();
      if (error) {
        setError(error);
        return null;
      }
      return data;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setError(error);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return {
    supabase,
    loading,
    error,
    query,
  };
} 